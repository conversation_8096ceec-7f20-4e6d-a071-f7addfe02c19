{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": 13133, "graphTooltip": 0, "id": 507, "iteration": 1679040345449, "links": [], "panels": [{"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "repeat": null, "title": "Overview", "type": "row"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 1}, "id": 19, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "increase(cloudflare_zone_requests_total{zone=~\"$zone\"}[$__range])", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Total Requests", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 1}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "increase(cloudflare_zone_bandwidth_total{zone=~\"$zone\"}[$__range])", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Total Bandwidth", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 1}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "rate(cloudflare_zone_requests_total{zone=~\"$zone\"}[$__interval])", "instant": false, "interval": "5m", "intervalFactor": 10, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Requests / second", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 12, "y": 1}, "id": 21, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "rate(cloudflare_zone_bandwidth_total{zone=~\"$zone\"}[$__interval])", "instant": false, "interval": "5m", "intervalFactor": 10, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Bandwidth / second", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 16, "y": 1}, "id": 20, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "increase(cloudflare_zone_uniques_total{zone=~\"$zone\"}[$__range])", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Total Unique Visitors", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 1}, "id": 23, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.4.2", "targets": [{"expr": "rate(cloudflare_zone_uniques_total{zone=~\"$zone\"}[$__interval])", "instant": false, "interval": "5m", "intervalFactor": 10, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Uniques / second", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_uniques_total{zone=~\"$zone\"}[$__interval])", "interval": "", "intervalFactor": 10, "legendFormat": "visitors", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unique Visitors", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 13, "x": 0, "y": 14}, "hiddenSeries": false, "id": 87, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=\"North America\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in North America / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 11, "x": 13, "y": 14}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=\"Europe\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in Europe / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 26, "panels": [], "title": "Requests", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_total{zone=~\"$zone\"}[$__interval])", "interval": "", "intervalFactor": 10, "legendFormat": "total", "refId": "A"}, {"expr": "rate(cloudflare_zone_requests_cached{zone=~\"$zone\"}[$__interval])", "interval": "", "intervalFactor": 10, "legendFormat": "cached", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests / second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\"}[$__interval]) > 0", "instant": false, "interval": "", "intervalFactor": 10, "legendFormat": "{{country}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests per Country / Second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_status{zone=~\"$zone\", status!~\"5..\"}[$__interval]) ", "interval": "", "intervalFactor": 10, "legendFormat": "{{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests / Second (status codes)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 89, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(cloudflare_zone_requests_status{zone=~\"$zone\", status!~\"2..\"}[$__interval]) ", "interval": "", "intervalFactor": 10, "legendFormat": "{{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests / Second (status codes not 2XX)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "percentunit", "gridPos": {"h": 11, "w": 6, "x": 0, "y": 40}, "id": 4, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "cloudflare_zone_requests_cached{zone=~\"$zone\"} / cloudflare_zone_requests_total{zone=~\"$zone\"}", "instant": true, "interval": "", "legendFormat": "cached", "refId": "A"}, {"expr": "(cloudflare_zone_requests_total{zone=~\"$zone\"} - cloudflare_zone_requests_cached{zone=~\"$zone\"}) / cloudflare_zone_requests_total{zone=~\"$zone\"}", "instant": true, "interval": "", "legendFormat": "uncached", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Cached Requests", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "percentunit", "gridPos": {"h": 11, "w": 6, "x": 6, "y": 40}, "id": 13, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "cloudflare_zone_requests_ssl_encrypted{zone=~\"$zone\"} / cloudflare_zone_requests_total{zone=~\"$zone\"}", "instant": true, "interval": "", "legendFormat": "encrypted", "refId": "A"}, {"expr": "( cloudflare_zone_requests_total{zone=~\"$zone\"} - cloudflare_zone_requests_ssl_encrypted{zone=~\"$zone\"}  ) / cloudflare_zone_requests_total{zone=~\"$zone\"}", "instant": true, "interval": "", "legendFormat": "unencrypted", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Requests SSL", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 40}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_status{zone=~\"$zone\", status=~\"5..\"}[$__interval]) ", "interval": "", "intervalFactor": 10, "legendFormat": "{{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Errors / Second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": "0.01"}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 11, "w": 12, "x": 12, "y": 49}, "id": 12, "interval": null, "legend": {"percentage": true, "show": true, "sort": "current", "sortDesc": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "increase(cloudflare_zone_requests_country{zone=~\"$zone\"}[$__range])", "instant": true, "interval": "", "legendFormat": "{{country}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Request Country", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": "0.001"}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 10, "w": 12, "x": 0, "y": 51}, "id": 11, "interval": null, "legend": {"percentage": true, "show": true, "sort": "current", "sortDesc": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "increase(cloudflare_zone_requests_content_type{zone=~\"$zone\"}[$__range])", "instant": true, "interval": "", "legendFormat": "{{content_type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Request Content Type", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": "0.00005"}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "short", "gridPos": {"h": 10, "w": 12, "x": 12, "y": 60}, "id": 28, "interval": null, "legend": {"percentage": true, "show": true, "sort": "current", "sortDesc": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "increase(cloudflare_zone_requests_status{zone=~\"$zone\"}[$__range])", "interval": "", "legendFormat": "{{ status }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Requests Status Codes", "type": "grafana-piechart-panel", "valueName": "current"}, {"collapsed": true, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 70}, "id": 30, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 23, "x": 0, "y": 16}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_bandwidth_total{zone=~\"$zone\"}[$__interval])", "interval": "5m", "intervalFactor": 10, "legendFormat": "total", "refId": "A"}, {"expr": "rate(cloudflare_zone_bandwidth_cached{zone=~\"$zone\"}[$__interval])", "interval": "5m", "intervalFactor": 10, "legendFormat": "cached", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bandwidth / second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "percentunit", "gridPos": {"h": 10, "w": 12, "x": 0, "y": 26}, "id": 9, "interval": null, "legend": {"show": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "cloudflare_zone_bandwidth_cached{zone=~\"$zone\"} / cloudflare_zone_bandwidth_total{zone=~\"$zone\"}", "interval": "", "legendFormat": "cached", "refId": "A"}, {"expr": "(cloudflare_zone_bandwidth_total{zone=~\"$zone\"} - cloudflare_zone_bandwidth_cached{zone=~\"$zone\"}) / cloudflare_zone_bandwidth_total{zone=~\"$zone\"}", "interval": "", "legendFormat": "uncached", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON><PERSON>", "type": "grafana-piechart-panel", "valueName": "current"}, {"aliasColors": {}, "breakPoint": "50%", "cacheTimeout": null, "combine": {"label": "Others", "threshold": 0}, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "80%", "format": "decbytes", "gridPos": {"h": 10, "w": 11, "x": 12, "y": 26}, "id": 15, "interval": null, "legend": {"percentage": true, "show": true, "sort": "current", "sortDesc": true, "values": true}, "legendType": "Right side", "links": [], "maxDataPoints": 1, "nullPointMode": "connected", "pieType": "pie", "strokeWidth": 1, "targets": [{"expr": "rate(cloudflare_zone_bandwidth_content_type{zone=~\"$zone\"}[5m])", "interval": "", "legendFormat": "{{content_type}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Bandwidth Content Type", "type": "grafana-piechart-panel", "valueName": "current"}], "title": "Bandwidth", "type": "row"}, {"collapsed": true, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 71}, "id": 38, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 39, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (region)(rate(cloudflare_zone_requests_country{zone=~\"$zone\"}[$__interval]))", "format": "time_series", "instant": false, "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ region }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests by Region / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (region)(rate(cloudflare_zone_bandwidth_country{zone=~\"$zone\"}[$__interval]))", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ region }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bandwidth by Region / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 34}, "hiddenSeries": false, "id": 41, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": "regions", "repeatDirection": "h", "scopedVars": {"regions": {"selected": true, "text": "Asia", "value": "Asia"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 34}, "hiddenSeries": false, "id": 82, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "repeatIteration": 1603353645365, "repeatPanelId": 41, "scopedVars": {"regions": {"selected": true, "text": "Europe", "value": "Europe"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 34}, "hiddenSeries": false, "id": 83, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "repeatIteration": 1603353645365, "repeatPanelId": 41, "scopedVars": {"regions": {"selected": true, "text": "South America", "value": "South America"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 41}, "hiddenSeries": false, "id": 84, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "repeatIteration": 1603353645365, "repeatPanelId": 41, "scopedVars": {"regions": {"selected": true, "text": "Oceania", "value": "Oceania"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 41}, "hiddenSeries": false, "id": 85, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "repeatIteration": 1603353645365, "repeatPanelId": 41, "scopedVars": {"regions": {"selected": true, "text": "North America", "value": "North America"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 41}, "hiddenSeries": false, "id": 86, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "maxPerRow": 3, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "repeatIteration": 1603353645365, "repeatPanelId": 41, "scopedVars": {"regions": {"selected": true, "text": "Africa", "value": "Africa"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_requests_country{zone=~\"$zone\", region=~\"$regions\"}[$__interval]) > 10", "interval": "5m", "intervalFactor": 10, "legendFormat": "{{ country }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests in $regions / second", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:125", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:126", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Regions", "type": "row"}, {"collapsed": true, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 72}, "id": 32, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 38}, "hiddenSeries": false, "id": 34, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_threats_total{zone=~\"$zone\"}[1m])", "interval": "", "legendFormat": "{{zone}}/threats", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Threats / second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:241", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:242", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 38}, "hiddenSeries": false, "id": 35, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_threats_country{zone=~\"$zone\"}[1m])", "interval": "", "legendFormat": "{{country}}/threats", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Threats / country", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:241", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:242", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 46}, "hiddenSeries": false, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_threats_type{zone=~\"$zone\"}[1m])", "interval": "", "legendFormat": "{{type}}/threats", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Threats / type", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:241", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:242", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Threats", "type": "row"}, {"collapsed": true, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 73}, "id": 48, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 74}, "hiddenSeries": false, "id": 50, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "percentage": false, "pluginVersion": "7.1.5", "pointradius": 2, "points": false, "renderer": "flot", "repeat": "zone", "scopedVars": {"zone": {"selected": true, "text": "diggysadventure.com", "value": "diggysadventure.com"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(cloudflare_zone_colocation_visits{zone=\"$zone\"}[$__interval]) > 0", "interval": "", "intervalFactor": 10, "legendFormat": "{{ colocation }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Visits  by Colocations / second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:169", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:170", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Colocations", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "egt-digital.com", "value": "egt-digital.com"}, "datasource": "Prometheus", "definition": "label_values(cloudflare_zone_requests_total, zone)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "zone", "options": [], "query": {"query": "label_values(cloudflare_zone_requests_total, zone)", "refId": "Prometheus-zone-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "Europe", "value": "Europe"}, "datasource": "Prometheus", "definition": "label_values(cloudflare_zone_requests_country, region)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": null, "multi": true, "name": "regions", "options": [], "query": {"query": "label_values(cloudflare_zone_requests_country, region)", "refId": "Prometheus-regions-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "CloudFlare Zone Analytics", "uid": "mWxtnz5Mz", "version": 1}