{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 545, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "MBs"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_virtualDisk_write_average{job=\"ews-telegraf\",disk=\"instance-total\",source!~\"vCLS-.*\",source=~\"bgsd-.*\"} / 1024", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "sum (vsphere_vm_virtualDisk_write_average{job=\"ews-telegraf\",disk=\"instance-total\",source!~\"vCLS-.*\",source=~\"bgsd-.*\"} / 1024)", "hide": false, "interval": "", "legendFormat": "SUM", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Virtual machine disk write average per VM - Dev", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "MBs", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "MBs"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 7}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_virtualDisk_write_average{job=\"ews-telegraf\",disk=\"instance-total\",source!~\"vCLS-.*\",source=~\"bgsp-.*\"} / 1024", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "sum (vsphere_vm_virtualDisk_write_average{job=\"ews-telegraf\",disk=\"instance-total\",source!~\"vCLS-.*\",source=~\"bgsp-.*\"} / 1024)", "hide": false, "interval": "", "legendFormat": "SUM", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Virtual machine disk write average per VM - Prod", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "MBs", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}, "unit": "MBs"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 14}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_virtualDisk_write_average{disk=\"instance-total\",source!~\"vCLS-.*\",job=\"ews-telegraf-kas\"} / 1024", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "sum (vsphere_vm_virtualDisk_write_average{disk=\"instance-total\",source!~\"vCLS-.*\",job=\"ews-telegraf-kas\"} / 1024)", "hide": false, "interval": "", "legendFormat": "SUM", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Virtual machine disk write average per VM - KAS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "MBs", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Disk writes per cluster", "uid": "918263yhbasdgct97123asd", "version": 3}