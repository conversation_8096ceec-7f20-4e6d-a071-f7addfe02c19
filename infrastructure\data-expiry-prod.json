{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 473, "links": [], "panels": [{"alert": {"alertRuleTags": {"severity": "critical"}, "conditions": [{"evaluator": {"params": [25200], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B", "12h", "now"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "1m", "handler": 1, "message": "Data Expiry deletion process duration is above 7 hours.", "name": "Data expiry deletion process duration alert", "noDataState": "alerting", "notifications": [{"uid": "I9C9By5Vk"}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select\n   p.start_time::date as time,\n  extract(epoch from (pr.min_date-p.start_time)) as expiry_duration,\n  extract(epoch from (p.end_time-pr.min_date))  as estimation_duration\n  from\n   data_expiry.process p,\n   (select process_id, min(start_time) as min_date from data_expiry.process_relation group by 1 ) pr\nwhere\n   pr.process_id=p.id\norder by 1", "refId": "B", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 25200, "visible": true}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Data expiry deletion process duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:69", "format": "dthms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:70", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"alertRuleTags": {"severity": "critical"}, "conditions": [{"evaluator": {"params": [1], "type": "lt"}, "operator": {"type": "and"}, "query": {"params": ["B", "12h", "now"]}, "reducer": {"params": [], "type": "last"}, "type": "query"}], "executionErrorState": "alerting", "for": "5m", "frequency": "1m", "handler": 1, "name": "Data expiry deleted rows per table alert", "noDataState": "alerting", "notifications": [{"uid": "I9C9By5Vk"}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 12}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select\n    start_time::date as time,\n    table_name::text,\n    sum(rows) as deleted_rows\nfrom data_expiry.process_relation\nWHERE \n  $__timeFilter(start_time)\ngroup by 1,2\norder by 2\n;", "refId": "B", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "lt", "value": 1, "visible": true}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Data expiry deleted rows per table", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:69", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:70", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-10d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Data expiry", "uid": "njJHJzK4z", "version": 1}