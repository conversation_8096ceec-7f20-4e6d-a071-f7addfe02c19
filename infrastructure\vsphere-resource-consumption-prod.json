{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1265, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-prod\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "AVG(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-prod\"})", "hide": false, "interval": "", "legendFormat": "AVG Cluster", "refId": "B"}, {"expr": "MIN(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-prod\"})", "hide": false, "interval": "", "legendFormat": "MIN Cluster", "refId": "C"}, {"expr": "MAX(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-prod\"})", "hide": false, "interval": "", "legendFormat": "MAX Cluster", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host cpu utilization average - PROD cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_mem_usage_average{clustername=\"sof1-prod\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host mem usage average - PROD cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-dev\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "AVG(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-dev\"})", "hide": false, "interval": "", "legendFormat": "AVG Cluster", "refId": "B"}, {"expr": "MIN(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-dev\"})", "hide": false, "interval": "", "legendFormat": "MIN Cluster", "refId": "C"}, {"expr": "MAX(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"sof1-dev\"})", "hide": false, "interval": "", "legendFormat": "MAX Cluster", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host cpu utilization average - Dev cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_mem_usage_average{clustername=\"sof1-dev\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host mem usage average - Dev cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:159", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:160", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"KAS-DR\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}, {"expr": "AVG(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"KAS-DR\"})", "hide": false, "interval": "", "legendFormat": "AVG Cluster", "refId": "B"}, {"expr": "MIN(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"KAS-DR\"})", "hide": false, "interval": "", "legendFormat": "MIN Cluster", "refId": "C"}, {"expr": "MAX(vsphere_host_cpu_usage_average{cpu=\"instance-total\",clustername=\"KAS-DR\"})", "hide": false, "interval": "", "legendFormat": "MAX Cluster", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host cpu utilization average - KAS cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:244", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:245", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_host_mem_usage_average{clustername=\"KAS-DR\"}", "interval": "", "legendFormat": "{{source}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "vsphere host mem usage average - KAS cluster", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:659", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:660", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "vSphere Resource Consumption", "uid": "9E6rv5E7z", "version": 5}