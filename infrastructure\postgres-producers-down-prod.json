{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 52, "links": [], "panels": [{"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT updated_date, active, correlation_id, name, description FROM sports_offering.feed_producers WHERE active=false\nORDER BY updated_date DESC", "refId": "A", "select": [[{"params": ["double_"], "type": "column"}]], "table": "act_hi_dec_in", "timeColumn": "create_time_", "timeColumnType": "timestamp", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Panel Title", "type": "table"}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Postgres Producers down", "uid": "05f0pm3Mzzzzz", "version": 5}