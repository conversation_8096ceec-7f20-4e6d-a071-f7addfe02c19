{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 0}, "id": 26, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"ParkedMessagesService\",topic=~\"feed-parked-updates\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup }}", "range": true, "refId": "A"}], "title": "FeedParkedUpdates LAG", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 8, "panels": [], "title": "SOF-PROD", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 9}, "id": 20, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"BetRadarConsumer_SOF-PROD\",topic=~\"feed-updates-betradar\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "FH.BR Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 9}, "id": 21, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"BetGeniusConsumer_SOF-PROD-FA\",topic=~\"feed-updates-betgenius\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "FH.BG Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 8, "y": 9}, "id": 22, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"LSportsConsumer_SOF-PROD\",topic=~\"feed-updates-lsports\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "FH.LS Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 12, "y": 9}, "id": 23, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"NotMappedEventsConsumer_SOF-PROD\",topic=~\"not-mapped-events\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}, {"expr": "sum(kafka_consumergroup_lag{consumergroup=~\"NotMappedEventsConsumer_Virtual_SOF-PROD\",topic=~\"not-mapped-events\"}) by (consumergroup)", "hide": false, "interval": "", "legendFormat": "{{consumergroup}}", "refId": "B"}], "title": "EventUnifier Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 16, "y": 9}, "id": 32, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"MarketUnifier_SOF-PROD\",topic=~\"sport.feed-aggregator.market-unifier\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "range": true, "refId": "A"}], "title": "MarketUnifier Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 20, "y": 9}, "id": 24, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"FeedAggregatorConsumer_SOF-PROD\",topic=~\"sport.feed-aggregator.feed-aggregator-adapted\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "range": true, "refId": "A"}, {"expr": "sum(kafka_consumergroup_lag{consumergroup=~\"FeedAggregatorConsumer_SOF-PROD_Standard\",topic=~\"sport.feed-aggregator.feed-aggregator-adapted\"}) by (consumergroup)", "hide": false, "interval": "", "legendFormat": "{{consumergroup}}", "refId": "B"}], "title": "FA Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 0, "y": 17}, "id": 29, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"ParkedMessagesService_SOF-PROD\",topic=~\"event-mappings\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "PMS Lag", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 500}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 4, "x": 4, "y": 17}, "id": 25, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(kafka_consumergroup_lag{consumergroup=~\"NotMappedTemplatesConsumer_SOF-PROD\",topic=~\"not-mapped-templates\"}) by (consumergroup)", "interval": "", "legendFormat": "{{consumergroup}}", "refId": "A"}], "title": "TemplateUnifier Lag", "type": "timeseries"}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Feeds Kafka Lag", "uid": "OpvddvvvdvslqRs1Iz", "version": 1}