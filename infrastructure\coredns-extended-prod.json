{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "A dashboard for the CoreDNS DNS server with updated metrics for version 1.7.0+.  Based on the CoreDNS dashboard by buhay.", "editable": true, "gnetId": 12539, "graphTooltip": 0, "id": 499, "iteration": 1677662839432, "links": [{"icon": "external link", "tags": [], "targetBlank": true, "title": "CoreDNS.io", "type": "link", "url": "https://coredns.io"}], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 38, "panels": [], "title": "Log Metrics", "type": "row"}, {"cacheTimeout": null, "datasource": "elasticsearch-coredns-logs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 12, "w": 5, "x": 0, "y": 1}, "id": 34, "links": [], "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "7.4.2", "targets": [{"alias": "", "bucketAggs": [{"field": "dns_question_name.keyword", "id": "1", "settings": {"min_doc_count": "0", "order": "desc", "orderBy": "1", "size": "10"}, "type": "terms"}], "metrics": [{"id": "1", "type": "count"}], "query": "", "refId": "A", "timeField": "@timestamp"}], "title": "Top Domain", "type": "table"}, {"cacheTimeout": null, "datasource": "elasticsearch-coredns-logs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 12, "w": 5, "x": 5, "y": 1}, "id": 39, "interval": null, "links": [], "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "7.4.2", "targets": [{"alias": "", "bucketAggs": [{"field": "dns_question_type.keyword", "id": "1", "settings": {"min_doc_count": "0", "order": "desc", "orderBy": "1", "size": "10"}, "type": "terms"}], "metrics": [{"id": "1", "type": "count"}], "query": "", "refId": "A", "timeField": "@timestamp"}], "title": "Top Query Type", "type": "table"}, {"cacheTimeout": null, "datasource": "elasticsearch-coredns-logs", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "gridPos": {"h": 12, "w": 5, "x": 10, "y": 1}, "id": 40, "interval": null, "links": [], "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "7.4.2", "targets": [{"alias": "", "bucketAggs": [{"field": "source_ip.keyword", "id": "1", "settings": {"min_doc_count": "0", "order": "desc", "orderBy": "1", "size": "10"}, "type": "terms"}], "metrics": [{"id": "1", "type": "count"}], "query": "", "refId": "A", "timeField": "@timestamp"}], "title": "Top Query Type", "type": "table"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 36, "panels": [], "title": "Prom Metrics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": [], "thresholds": {"mode": "absolute", "steps": []}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 14}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "yaxis": 2}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_dns_request_count_total{instance=~\"$instance\"}[5m])) by (proto) or\nsum(rate(coredns_dns_requests_total{instance=~\"$instance\"}[5m])) by (proto)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (total)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 14}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "yaxis": 2}, {"alias": "other", "yaxis": 2}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_dns_request_type_count_total{instance=~\"$instance\"}[5m])) by (type) or \nsum(rate(coredns_dns_requests_total{instance=~\"$instance\"}[5m])) by (type)", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (by qtype)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 14}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "yaxis": 2}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_dns_request_count_total{instance=~\"$instance\"}[5m])) by (zone) or\nsum(rate(coredns_dns_requests_total{instance=~\"$instance\"}[5m])) by (zone)", "interval": "", "intervalFactor": 2, "legendFormat": "{{zone}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (by zone)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_dns_request_do_count_total{instance=~\"$instance\"}[5m])) or\nsum(rate(coredns_dns_do_requests_total{instance=~\"$instance\"}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "DO", "refId": "A", "step": 40}, {"expr": "sum(rate(coredns_dns_request_count_total{instance=~\"$instance\"}[5m])) or\nsum(rate(coredns_dns_requests_total{instance=~\"$instance\"}[5m]))", "interval": "", "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (DO bit)", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "pps", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 21}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "tcp:90", "yaxis": 2}, {"alias": "tcp:99 ", "yaxis": 2}, {"alias": "tcp:50", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto))", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:99 ", "refId": "A", "step": 60}, {"expr": "histogram_quantile(0.90, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto))", "intervalFactor": 2, "legendFormat": "{{proto}}:90", "refId": "B", "step": 60}, {"expr": "histogram_quantile(0.50, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto))", "intervalFactor": 2, "legendFormat": "{{proto}}:50", "refId": "C", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (size, udp)", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 21}, "hiddenSeries": false, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "tcp:90", "yaxis": 1}, {"alias": "tcp:99 ", "yaxis": 1}, {"alias": "tcp:50", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le,proto))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:99 ", "refId": "A", "step": 60}, {"expr": "histogram_quantile(0.90, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le,proto))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:90", "refId": "B", "step": 60}, {"expr": "histogram_quantile(0.50, sum(rate(coredns_dns_request_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le,proto))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:50", "refId": "C", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests (size,tcp)", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 28}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_dns_response_rcode_count_total{instance=~\"$instance\"}[5m])) by (rcode) or\nsum(rate(coredns_dns_responses_total{instance=~\"$instance\"}[5m])) by (rcode)", "interval": "", "intervalFactor": 2, "legendFormat": "{{rcode}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Responses (by rcode)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(coredns_dns_request_duration_seconds_bucket{instance=~\"$instance\"}[5m])) by (le, job))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 40}, {"expr": "histogram_quantile(0.90, sum(rate(coredns_dns_request_duration_seconds_bucket{instance=~\"$instance\"}[5m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90%", "refId": "B", "step": 40}, {"expr": "histogram_quantile(0.50, sum(rate(coredns_dns_request_duration_seconds_bucket{instance=~\"$instance\"}[5m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "50%", "refId": "C", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Responses (duration)", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "udp:50%", "yaxis": 1}, {"alias": "tcp:50%", "yaxis": 2}, {"alias": "tcp:90%", "yaxis": 2}, {"alias": "tcp:99%", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto)) ", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:99%", "refId": "A", "step": 40}, {"expr": "histogram_quantile(0.90, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto)) ", "interval": "", "intervalFactor": 2, "legendFormat": "{{proto}}:90%", "refId": "B", "step": 40}, {"expr": "histogram_quantile(0.50, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"udp\"}[5m])) by (le,proto)) ", "hide": false, "intervalFactor": 2, "legendFormat": "{{proto}}:50%", "metric": "", "refId": "C", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Responses (size, udp)", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 35}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "udp:50%", "yaxis": 1}, {"alias": "tcp:50%", "yaxis": 1}, {"alias": "tcp:90%", "yaxis": 1}, {"alias": "tcp:99%", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le,proto)) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{proto}}:99%", "refId": "A", "step": 40}, {"expr": "histogram_quantile(0.90, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le,proto)) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{proto}}:90%", "refId": "B", "step": 40}, {"expr": "histogram_quantile(0.50, sum(rate(coredns_dns_response_size_bytes_bucket{instance=~\"$instance\",proto=\"tcp\"}[5m])) by (le, proto)) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{proto}}:50%", "metric": "", "refId": "C", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Responses (size, tcp)", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 42}, "hiddenSeries": false, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(coredns_cache_size{instance=~\"$instance\"}) by (type) or\nsum(coredns_cache_entries{instance=~\"$instance\"}) by (type)", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Cache (size)", "tooltip": {"shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 42}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "misses", "yaxis": 2}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(coredns_cache_hits_total{instance=~\"$instance\"}[5m])) by (type)", "hide": false, "intervalFactor": 2, "legendFormat": "hits:{{type}}", "refId": "A", "step": 40}, {"expr": "sum(rate(coredns_cache_misses_total{instance=~\"$instance\"}[5m])) by (type)", "hide": false, "intervalFactor": 2, "legendFormat": "misses", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON> (hitrate)", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "pps", "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "default", "value": "default"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "$datasource", "definition": "label_values(up{endpoint=\"http-metrics\", job=~\"coredns.*\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(up{endpoint=\"http-metrics\", job=~\"coredns.*\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "utc", "title": "CoreDNS Extended", "uid": "iUu3OuA4z", "version": 1}