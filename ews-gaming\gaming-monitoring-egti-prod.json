{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 2, "id": 1162, "iteration": 1640001827533, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(http_server_requests_seconds_count[1m]))", "format": "time_series", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Overall load", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:238", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:239", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 0}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(http_server_requests_seconds_count{outcome!=\"SUCCESS\"}[5m])", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error calls to Gaming.", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:290", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:291", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 0}, "hiddenSeries": false, "id": 41, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(gamesession_findbysessionid_and_gameprovider_notfound_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "CheckForNewGameSessionLongest {{provider}}", "refId": "CheckForNewGameSession"}, {"expr": "rate(gamesession_findbysessionid_and_gameprovider_cache_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "FoundGSinCache {{provider}}", "refId": "ChecForExistingGameSession - cache"}, {"expr": "rate(gamesession_findbysessionid_and_gameprovider_cache_archive_max[1m])", "hide": false, "interval": "", "legendFormat": "FoundGSinArchive {{provider}}", "refId": "CheckForExistingGameSession - archive"}, {"expr": "rate(gamesession_findbysessionid_notfound_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "CheckForNewGameSessionLongest {{provider}}", "refId": "CheckForNewGameSession-onlyid"}, {"expr": "rate(gamesession_findbysessionid_cache_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "FoundGSinCache {{provider}}", "refId": "ChecForExistingGameSession - cache-onlyid"}, {"expr": "rate(gamesession_findbysessionid_notfound_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "CheckForNewGameSessionLongest {{provider}}", "refId": "CheckForExistingGameSession - archive -onlyid"}, {"expr": "rate(gamesession_create_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "CreateSessionMaxTime {{provider}}", "refId": "CreateGameSession - max time"}, {"expr": "rate(gamesession_create_seconds_count[1m])", "hide": false, "interval": "", "legendFormat": "CreatedSessionsCount {{provider}}", "refId": "CreateGameSession - count"}, {"expr": "rate(gamesession_terminatesession_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "TerminateSessionaMaxTime - {{provider}}", "refId": "TerminataGameSession - max time"}, {"expr": "rate(gamesession_terminatesession_seconds_count[1m])", "hide": false, "interval": "", "legendFormat": "TerminateSessionaCount - {{provider}}", "refId": "TerminationGameSession - count"}, {"expr": "rate(cache_restoration_time_seconds_max[1m])", "hide": false, "interval": "", "legendFormat": "CacheRestoreMaxTime {{provider}}", "refId": "CacheRestorationTime"}, {"expr": "rate(cache_restoration_count_total[1m])", "hide": false, "interval": "", "legendFormat": "CacheRestoreInstanceCount  {{provider}}", "refId": "CacheRestorationCount"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Game Session stats", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:498", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:499", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-gaming-wallet-egti", "value": "ews-gaming-wallet-egti"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-gaming-wallet-egti.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "************:10200", "value": "************:10200"}, "datasource": null, "definition": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "GAMING Monitoring EGTi", "uid": "et<PERSON><PERSON><PERSON>", "version": 3}