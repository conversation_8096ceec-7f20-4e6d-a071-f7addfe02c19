{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4813, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 54, "panels": [], "title": "Seamless Wallet API (UAT)", "type": "row"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "dn9yUbx7k"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 1}, "id": 55, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "dn9yUbx7k"}, "editorMode": "code", "format": "time_series", "rawQuery": true, "rawSql": "SELECT\r\n    a.created_date AS time,\r\n    a.business_unit,\r\n    a.ticket_id::text\r\nFROM integration.audit_log a\r\nLEFT JOIN integration.audit_log b\r\n    ON a.ticket_id = b.ticket_id\r\n    AND a.id <> b.id\r\n    AND b.event_type IN ('TicketAcceptedRequestDto', 'TicketRejectedRequestDto')\r\n    AND b.response_status_code = 200\r\n    AND (b.response_payload::JSONB)->>'errorCode' = '0'\r\nWHERE a.event_type = 'TicketReserveAmountRequestDto'\r\n  AND a.ticket_id != 0\r\n  AND a.response_status_code = 200\r\n  AND (a.response_payload::JSONB)->>'errorCode' = '0'\r\n  AND b.id IS NULL  -- Ensuring missing Ticket Accepted/Rejected\r\n  AND $__timeFilter(a.created_date) \r\n  AND a.created_date < NOW() - INTERVAL '20 seconds'\t-- assure not in progress\r\nORDER BY time;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Missing TicketAccepted / TicketRejected Calls", "type": "table"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "dn9yUbx7k"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 1}, "id": 56, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "dn9yUbx7k"}, "editorMode": "code", "format": "time_series", "rawQuery": true, "rawSql": "WITH req_amount AS (\r\n    SELECT\r\n        a.id,\r\n        a.created_date,\r\n        a.ticket_id,\r\n        a.business_unit,\r\n        a.request_payload->>'TicketAmount' AS request_amount,\r\n        COALESCE((a.response_payload::JSONB)->>'reservedAmount', \r\n                a.request_payload->>'TicketAmount') AS reserved_amount,\r\n        (a.response_payload::JSONB)->>'reserveId' AS reserved_id,\r\n        b.request_payload->>'ticketAmount' AS ticket_amount,\r\n        b.request_payload->'bets'->0->>'isFreeBet' AS is_free_bet\r\n    FROM integration.audit_log a\r\n    LEFT JOIN integration.audit_log b\r\n        ON a.ticket_id = b.ticket_id\r\n        AND a.id != b.id\r\n        AND b.event_type = 'TicketAcceptedRequestDto'\r\n        AND b.response_status_code = 200\r\n    WHERE a.event_type = 'TicketReserveAmountRequestDto'\r\n        AND a.ticket_id != 0\r\n        AND a.response_status_code = 200\r\n        AND (a.response_payload::JSONB)->>'errorCode' = '0'\r\n        AND b.event_type IS NOT NULL\r\n        -- AND a.created_date > '2025-02-12 11:00:41.198'\r\n        AND $__timeFilter(a.created_date)\r\n),\r\nticket_amounts AS (\r\n    SELECT\r\n        t.id,\r\n        t.player_id,\r\n        COALESCE(SUM((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0), 0) AS main_account_stake,\r\n        COALESCE(SUM((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0), 0) AS bonus_account_amount\r\n    FROM integration.audit_log al\r\n    JOIN betting.ticket t ON t.id = al.ticket_id\r\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\r\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\r\n        AND al.response_status_code = 200\r\n        -- AND al.created_date > '2025-02-12 11:00:41.198'\r\n        AND $__timeFilter(al.created_date)\r\n    GROUP BY t.id, t.player_id\r\n),\r\nticket_partial_cashouts AS (\r\n    SELECT\r\n        t.id,\r\n        t.player_id,\r\n        COALESCE(SUM((evts->>'usedStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_stake,\r\n        COALESCE(SUM((evts->>'usedBonusStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_bonus_stake\r\n    FROM integration.audit_log al\r\n    JOIN betting.ticket t ON t.id = al.ticket_id\r\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\r\n    LEFT JOIN LATERAL jsonb_array_elements(bts->'events') AS evts ON TRUE\r\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\r\n        AND al.response_status_code = 200\r\n        -- AND al.created_date > '2025-02-12 11:00:41.198'\r\n        AND $__timeFilter(al.created_date)\r\n    GROUP BY t.id, t.player_id\r\n)\r\nselect\r\n    ra.created_date as time,\r\n    ra.business_unit,\r\n    ra.ticket_id::text    \r\n    /*   \r\n    ta.player_id,\r\n    ra.reserved_id,\r\n    ra.request_amount,\r\n    ra.reserved_amount,\r\n    ra.ticket_amount,\r\n    ra.is_free_bet,\r\n    ta.main_account_stake,\r\n    ta.bonus_account_amount,\r\n    tpc.used_stake,\r\n    tpc.used_bonus_stake,\r\n    ra.reserved_amount::DECIMAL - COALESCE(ta.main_account_stake, 0) - tpc.used_stake AS main_account_diff,\r\n    CASE\r\n        WHEN ra.is_free_bet = 'true' THEN 0\r\n        ELSE ta.bonus_account_amount - (ra.request_amount::DECIMAL - ra.reserved_amount::DECIMAL)\r\n    END AS bonus_account_diff*/\r\nFROM req_amount ra\r\nJOIN ticket_amounts ta ON ra.ticket_id = ta.id\r\nJOIN ticket_partial_cashouts tpc ON ra.ticket_id = tpc.id\r\nWHERE ra.reserved_amount::DECIMAL != COALESCE(ta.main_account_stake, 0) + tpc.used_stake\r\n    OR (\r\n        ra.is_free_bet != 'true'\r\n        AND ta.bonus_account_amount != (ra.request_amount::DECIMAL - ra.reserved_amount::DECIMAL)\r\n    )\r\nORDER BY created_date;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Inconsistent Reserved vs Accepted / Rejected Amounts", "type": "table"}, {"datasource": {"uid": "dn9yUbx7k"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "noValue": "ok", "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}, {"color": "dark-red", "value": 0}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 1}, "id": 57, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.3.0", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH bets AS (\r\n\tSELECT\r\n\t    t.id AS ticket_id,\r\n\t    t.player_id,\r\n\t    t.business_unit,\r\n\t    t.updated_date,\r\n\t    bts->'id' AS bet_id,\r\n\t    (bts->'betStatus')::text AS bet_status,\r\n\t    (bts->'outcomeStatus')::text AS bet_outcome_status,\r\n\t    COALESCE((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0, 0) AS stake_main_acc_amount,\r\n\t    COALESCE((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0, 0) AS stake_bonus_acc_amount,\r\n\t    COALESCE((bts->'cashoutStake'->>'valueLong')::BIGINT / 10000.0, 0) AS stake_cashed_out,\r\n\t    SUM(COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0)) as sent_win_amount,\r\n\t\tSUM(COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0)) as sent_refund_amount,\r\n\t\tSUM(COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0)) as sent_amend_amount,\r\n\t\tSUM(COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0)) as sent_cashout_amount,\r\n\t\tSUM(COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0)) as sent_bonus_amount,\r\n\t\tSUM(COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)) as sent_revert_amount,\r\n\t\tSUM(\r\n\t        COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0) +\r\n\t        COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0) +\r\n\t        COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0) +\r\n\t        COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0) +\r\n\t        COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0) +\t        \r\n\t        COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)\r\n\t    ) AS total_sent_amount\r\n\tFROM betting.ticket t\r\n\tCROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\r\n\tLEFT JOIN integration.audit_log al \r\n\t    ON al.ticket_id = t.id\r\n\t    AND al.response_status_code = 200\r\n\t    AND al.created_date >= t.created_date \r\n\t    AND al.event_type IN ('BetWonRequestDto', 'BetLostRequestDto', 'BetRefundedRequestDto', 'BetAmendedRequestDto',\r\n\t    \t\t\t\t\t\t'BetCashedOutRequestDto', 'BetBonusAwardDto', 'BetBonusRevertDto')\r\n\t    AND al.request_payload->>'betId' = bts->>'id'\r\n\tWHERE t.business_unit IN ('WNRS','EVBA','SLPH')\r\n\t\t  -- AND t.updated_date > '2025-01-26 13:42:14'\r\n          AND $__timeFilter(t.updated_date)\r\n\t      AND t.status NOT IN ('REJECTED', 'ACCEPTED')\r\n\tGROUP BY t.id, t.player_id, t.business_unit, bts\r\n),\r\nmetadata AS (\r\n    SELECT\r\n        t.id AS ticket_id,\r\n        smry->'betId' AS bet_id,\r\n        COALESCE((smry->'totalWinLong')::BIGINT / 10000.0, 0) AS total_win,\r\n        COALESCE((smry->'winGrossLong')::BIGINT / 10000.0, 0) AS paid_win,\r\n        COALESCE((smry->'bonusWinLong')::BIGINT / 10000.0, 0) AS bonus_win,\r\n        COALESCE((SELECT SUM((campaign->>'amount')::DECIMAL) FROM jsonb_array_elements(smry->'campaigns') AS campaign), 0) AS total_paid_campaign_amount\r\n    FROM betting.ticket t\r\n    CROSS JOIN LATERAL jsonb_array_elements(t.meta_data->'summaries') AS smry\r\n    JOIN integration.business_unit_api_config buac \r\n        ON buac.business_unit = t.business_unit\r\n    WHERE EXISTS (SELECT 1 FROM bets WHERE bets.ticket_id = t.id)\r\n),\r\nvalidation AS (\r\n    SELECT\r\n        bets.updated_date AS time,\r\n        bets.business_unit,\r\n        bets.ticket_id,\r\n        CASE \r\n            WHEN bets.bet_status = '\"CASHED_OUT\"' THEN\r\n                CASE \r\n                    WHEN bets.total_sent_amount = bets.stake_cashed_out THEN 'ok' \r\n                    ELSE 'different cash out amount' \r\n                END\r\n            WHEN bets.bet_outcome_status = '\"VOID\"' THEN \r\n                CASE \r\n                    WHEN bets.total_sent_amount = bets.stake_main_acc_amount THEN 'ok'\r\n\t\t        \tELSE 'invalid loss amount'\r\n                END\r\n            WHEN bets.bet_outcome_status = '\"CANCELED\"' THEN \r\n                CASE \r\n                    WHEN bets.stake_main_acc_amount = bets.sent_amend_amount \r\n                    \tOR bets.stake_main_acc_amount = bets.total_sent_amount \r\n                        OR bets.stake_bonus_acc_amount = bets.sent_amend_amount\r\n                    THEN 'ok' \r\n                    ELSE 'different win amount' \r\n                END\r\n\t        WHEN bets.bet_outcome_status = '\"LOSS\"' THEN\r\n\t        \tCASE \r\n\t\t        \tWHEN bets.total_sent_amount = 0 AND metadata.total_win = 0 THEN 'ok'\r\n\t\t        \tWHEN bets.total_sent_amount = bets.sent_bonus_amount \r\n                        AND (bets.total_sent_amount = metadata.bonus_win + metadata.total_paid_campaign_amount) \r\n                    THEN 'ok'\r\n\t\t        \tELSE 'invalid loss amount'\r\n\t\t        END\r\n            WHEN bets.bet_outcome_status = '\"WON\"' THEN \r\n                CASE \r\n\t                WHEN bets.total_sent_amount = metadata.total_win + bets.stake_cashed_out + metadata.total_paid_campaign_amount \r\n\t                THEN 'ok' \r\n                    WHEN ABS(bets.total_sent_amount - metadata.total_win) < 0.1\r\n                           OR (bets.total_sent_amount = metadata.total_win + metadata.bonus_win)\r\n                           OR (bets.total_sent_amount = 0 AND bets.stake_main_acc_amount = 0)\r\n                    THEN 'ok' \r\n                    ELSE 'different win amount'\r\n                END\r\n\t\t\tELSE 'not handled'\r\n        END AS validation_error\r\n    FROM bets\r\n    JOIN metadata ON bets.ticket_id = metadata.ticket_id AND bets.bet_id = metadata.bet_id\r\n)\r\nSELECT \r\n    time,\r\n    business_unit,\r\n    validation_error,\r\n    ticket_id::text\r\nFROM validation\r\nWHERE validation_error != 'ok'\r\nGROUP BY time, business_unit, ticket_id, validation_error\r\nORDER BY time DESC;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Settlement balance differences", "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 34, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-proxy-api\",namespace=\"$namespace\"}[1m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-message-router-service\",namespace=\"$namespace\"}[1m])) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-message-processor-service\",namespace=\"$namespace\"}[1m])) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "C"}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}, "id": 36, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-proxy-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-message-router-service\",namespace=\"$namespace\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-message-processor-service\",namespace=\"$namespace\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "C"}], "title": "Healthchecks", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}, "id": 41, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "cache_items_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\", cache=\"DeadLetterKafkaCache\"}", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "Dead Letter Queue (Items Count)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 43, "panels": [], "title": "Outbound Proxy API", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 44, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 45, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 47, "panels": [], "title": "Outbound Message Processor", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 50, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 51, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 49, "panels": [], "title": "Outbound Message Router", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}, "id": 52, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}, "id": 53, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 45}, "id": 40, "panels": [{"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_open_handles{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_working_set_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_num_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}], "title": "Resources", "type": "row"}], "preload": false, "refresh": "1m", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "", "value": ""}, "label": "Feed Type", "name": "feed_type", "options": [{"selected": true, "text": "Premium", "value": ""}, {"selected": false, "text": "Standard", "value": "standard-"}], "query": "Premium :   ,Standard : standard-", "type": "custom"}, {"current": {"text": "ews-outbound-proxy-api", "value": "ews-outbound-proxy-api"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{}, job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/^${feed_type}ews-outbound.*/", "type": "query"}, {"current": {"text": "ews-dev", "value": "ews-dev"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "172.17.36.205:80", "value": "172.17.36.205:80"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "includeAll": false, "label": "Instance", "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "ews-outbound-proxy-api-7b694fc5bf-fq8sr", "value": "ews-outbound-proxy-api-7b694fc5bf-fq8sr"}, "datasource": {"type": "prometheus"}, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "hide": 2, "includeAll": false, "label": "Pod", "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "ews-outbound-proxy-api-7b694fc5bf-fq8sr", "value": "ews-outbound-proxy-api-7b694fc5bf-fq8sr"}, "datasource": {"type": "prometheus"}, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "includeAll": false, "label": "Podname", "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Outbound Proxy (Integrations)", "uid": "wf_6g8aVz", "version": 1, "weekStart": ""}