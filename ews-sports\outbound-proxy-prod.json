{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1741070594914, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 59, "panels": [], "title": "Seamless Wallet API (SOF-PROD)", "type": "row"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "noValue": "ok", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 1}, "id": 61, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n    a.created_date as time,\n    a.business_unit,\n    ':' || a.ticket_id::text as ticket_id\n    --COUNT(*) AS count\nFROM integration.audit_log a\nLEFT JOIN integration.audit_log b\n    ON a.ticket_id = b.ticket_id\n    AND a.id <> b.id\n    AND b.event_type IN ('TicketAcceptedRequestDto', 'TicketRejectedRequestDto')\n    AND b.response_status_code = 200\n    AND (b.response_payload::JSONB)->>'errorCode' = '0'\nWHERE a.event_type = 'TicketReserveAmountRequestDto'\n  AND a.ticket_id != 0\n  AND a.response_status_code = 200\n  AND (a.response_payload::JSONB)->>'errorCode' = '0'\n  AND b.id IS NULL  -- Ensuring missing Ticket Accepted/Rejected\n  AND $__timeFilter(a.created_date) \n  AND a.created_date < NOW() - INTERVAL '20 seconds'\t-- assure not in progress\n--GROUP BY a.business_unit\norder by a.created_date desc;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Missing TicketAccepted / TicketRejected", "type": "table"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "noValue": "ok", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 1}, "id": 62, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH req_amount AS (\n    SELECT\n        a.id,\n        a.created_date,\n        a.ticket_id,\n        a.business_unit,\n        a.request_payload->>'TicketAmount' AS request_amount,\n        COALESCE((a.response_payload::JSONB)->>'reservedAmount', \n                a.request_payload->>'TicketAmount') AS reserved_amount,\n        (a.response_payload::JSONB)->>'reserveId' AS reserved_id,\n        b.request_payload->>'ticketAmount' AS ticket_amount,\n        b.request_payload->'bets'->0->>'isFreeBet' AS is_free_bet\n    FROM integration.audit_log a\n    LEFT JOIN integration.audit_log b\n        ON a.ticket_id = b.ticket_id\n        AND a.id != b.id\n        AND b.event_type = 'TicketAcceptedRequestDto'\n        AND b.response_status_code = 200\n    WHERE a.event_type = 'TicketReserveAmountRequestDto'\n        AND a.ticket_id != 0\n        AND a.response_status_code = 200\n        AND (a.response_payload::JSONB)->>'errorCode' = '0'\n        AND b.event_type IS NOT NULL\n        -- AND a.created_date > '2025-02-12 11:00:41.198'\n        AND $__timeFilter(a.created_date)\n),\nticket_amounts AS (\n    SELECT\n        t.id,\n        t.player_id,\n        COALESCE(SUM((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0), 0) AS main_account_stake,\n        COALESCE(SUM((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0), 0) AS bonus_account_amount\n    FROM integration.audit_log al\n    JOIN betting.ticket t ON t.id = al.ticket_id\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\n        AND al.response_status_code = 200\n        -- AND al.created_date > '2025-02-12 11:00:41.198'\n        AND $__timeFilter(al.created_date)\n    GROUP BY t.id, t.player_id\n),\nticket_partial_cashouts AS (\n    SELECT\n        t.id,\n        t.player_id,\n        COALESCE(SUM((evts->>'usedStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_stake,\n        COALESCE(SUM((evts->>'usedBonusStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_bonus_stake\n    FROM integration.audit_log al\n    JOIN betting.ticket t ON t.id = al.ticket_id\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n    LEFT JOIN LATERAL jsonb_array_elements(bts->'events') AS evts ON TRUE\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\n        AND al.response_status_code = 200\n        -- AND al.created_date > '2025-02-12 11:00:41.198'\n        AND $__timeFilter(al.created_date)\n    GROUP BY t.id, t.player_id\n)\nselect\n    ra.created_date as time,\n    ra.business_unit,\n    ':' || ta.player_id::text as player_id,\n    ':' || ra.ticket_id::text as ticket_id\n    /*   \n    ra.ticket_id,\n    ra.created_date,\n    ra.business_unit,\n    ta.player_id,\n    ra.reserved_id,\n    ra.request_amount,\n    ra.reserved_amount,\n    ra.ticket_amount,\n    ra.is_free_bet,\n    ta.main_account_stake,\n    ta.bonus_account_amount,\n    tpc.used_stake,\n    tpc.used_bonus_stake,\n    ra.reserved_amount::DECIMAL - COALESCE(ta.main_account_stake, 0) - tpc.used_stake AS main_account_diff,\n    CASE\n        WHEN ra.is_free_bet = 'true' THEN 0\n        ELSE ta.bonus_account_amount - (ra.request_amount::DECIMAL - ra.reserved_amount::DECIMAL)\n    END AS bonus_account_diff*/\nFROM req_amount ra\nJOIN ticket_amounts ta ON ra.ticket_id = ta.id\nJOIN ticket_partial_cashouts tpc ON ra.ticket_id = tpc.id\nWHERE ra.reserved_amount::DECIMAL != COALESCE(ta.main_account_stake, 0) + tpc.used_stake\n    OR (\n        ra.is_free_bet != 'true'\n        AND ta.bonus_account_amount != (ra.request_amount::DECIMAL - ra.reserved_amount::DECIMAL)\n    )\nORDER BY ra.created_date desc;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Insconsistent Reserved vs Accepted / Rejected Amounts", "type": "table"}, {"datasource": "PostgreSQL DWH", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "noValue": "ok", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 1}, "id": 63, "options": {"showHeader": true}, "pluginVersion": "7.4.2", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH bets AS (\n\tSELECT\n\t    t.id AS ticket_id,\n\t    t.player_id,\n\t    t.business_unit,\n\t    t.updated_date,\n\t    bts->'id' AS bet_id,\n\t    (bts->'betStatus')::text AS bet_status,\n\t    (bts->'outcomeStatus')::text AS bet_outcome_status,\n\t    COALESCE((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0, 0) AS stake_main_acc_amount,\n\t    COALESCE((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0, 0) AS stake_bonus_acc_amount,\n\t    COALESCE((bts->'cashoutStake'->>'valueLong')::BIGINT / 10000.0, 0) AS stake_cashed_out,\n\t    COALESCE((bts->'cashoutBonusStake'->>'valueLong')::BIGINT / 10000.0, 0) AS stake_cashed_out_bonnus,\n\t    SUM(COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0)) as sent_win_amount,\n\t\tSUM(COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0)) as sent_refund_amount,\n\t\tSUM(COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0)) as sent_amend_amount,\n\t\tSUM(COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0)) as sent_cashout_amount,\n\t\tSUM(COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0)) as sent_bonus_amount,\n\t\tSUM(COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)) as sent_revert_amount,\n\t\tSUM(\n\t        COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0) +\t        \n\t        COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)\n\t    ) AS total_sent_amount\n\tFROM betting.ticket t\n\tCROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n\tLEFT JOIN integration.audit_log al \n\t    ON al.ticket_id = t.id\n\t    AND al.response_status_code = 200\n\t    AND al.created_date >= t.created_date \n\t    AND al.event_type IN ('BetWonRequestDto', 'BetLostRequestDto', 'BetRefundedRequestDto', 'BetAmendedRequestDto',\n\t    \t\t\t\t\t\t'BetCashedOutRequestDto', 'BetBonusAwardDto', 'BetBonusRevertDto')\n\t    AND al.request_payload->>'betId' = bts->>'id'\n\tWHERE t.business_unit IN ('WNRS','EVBA','SLPH')\n\t\t    --- AND t.updated_date > '2025-01-22 13:42:14'\n        AND $__timeFilter(t.updated_date)\n\t      AND t.status NOT IN ('REJECTED', 'ACCEPTED')\n\tGROUP BY t.id, t.player_id, t.business_unit, bts\n),\nmetadata AS (\n    SELECT\n        t.id AS ticket_id,\n        smry->'betId' AS bet_id,\n        COALESCE((smry->'totalWinLong')::BIGINT / 10000.0, 0) AS total_win,\n        COALESCE((smry->'winGrossLong')::BIGINT / 10000.0, 0) AS paid_win,\n        COALESCE((smry->'bonusWinLong')::BIGINT / 10000.0, 0) AS bonus_win,\n        COALESCE((SELECT SUM((campaign->>'amount')::DECIMAL) FROM jsonb_array_elements(smry->'campaigns') AS campaign), 0) AS total_paid_campaign_amount\n    FROM betting.ticket t\n    CROSS JOIN LATERAL jsonb_array_elements(t.meta_data->'summaries') AS smry\n    JOIN integration.business_unit_api_config buac \n        ON buac.business_unit = t.business_unit\n    WHERE EXISTS (SELECT 1 FROM bets WHERE bets.ticket_id = t.id)\n),\nvalidation AS (\n    SELECT\n        bets.updated_date AS time,\n        bets.business_unit,\n        bets.player_id::text,\n        bets.ticket_id::text,\n        CASE \n            WHEN bets.bet_status = '\"CASHED_OUT\"' THEN\n                CASE \n                    WHEN bets.total_sent_amount = bets.stake_cashed_out THEN 'ok' \n                    WHEN ABS(bets.total_sent_amount - bets.stake_cashed_out + bets.stake_cashed_out_bonnus) <= 0.1 THEN 'ok' \n                    ELSE 'different cash out amount' \n                END\n            WHEN bets.bet_outcome_status = '\"VOID\"' THEN \n                CASE \n                    WHEN bets.total_sent_amount = bets.stake_main_acc_amount THEN 'ok'\n\t\t        \tELSE 'invalid loss amount'\n                END\n            WHEN bets.bet_outcome_status = '\"CANCELED\"' THEN \n                CASE \n                    WHEN (bets.stake_main_acc_amount + bets.stake_bonus_acc_amount) = bets.sent_amend_amount \n                    \tOR bets.stake_main_acc_amount = bets.total_sent_amount \n                        OR bets.stake_bonus_acc_amount = bets.sent_amend_amount\n                    THEN 'ok' \n                    ELSE 'different amend amount' \n                END\n\t        WHEN bets.bet_outcome_status = '\"LOSS\"' THEN\n\t        \tCASE \n\t\t        \tWHEN bets.total_sent_amount = 0 AND metadata.total_win = 0 THEN 'ok'\n\t\t        \tWHEN bets.total_sent_amount = bets.sent_bonus_amount \n                        AND (bets.total_sent_amount = metadata.bonus_win + metadata.total_paid_campaign_amount) \n                    THEN 'ok'\n\t\t        \tWHEN bets.total_sent_amount = bets.sent_cashout_amount \n                        AND (bets.total_sent_amount = bets.stake_cashed_out) \n                    THEN 'ok'\n\t\t        \tELSE 'invalid loss amount'\n\t\t        END\n            WHEN bets.bet_outcome_status = '\"WON\"' THEN \n                CASE \n\t                WHEN bets.total_sent_amount = metadata.total_win + bets.stake_cashed_out + metadata.total_paid_campaign_amount \n\t                THEN 'ok' \n                    WHEN ABS(bets.total_sent_amount - metadata.total_win) < 0.1\n                           OR (bets.total_sent_amount = metadata.total_win + metadata.bonus_win)\n                           OR (bets.total_sent_amount = 0 AND bets.stake_main_acc_amount = 0)\n                    THEN 'ok' \n                    ELSE 'different win amount'\n                END\n\t\t\tELSE 'not handled'\n        END AS validation_error\n    FROM bets\n    JOIN metadata ON bets.ticket_id = metadata.ticket_id AND bets.bet_id = metadata.bet_id\n)\nSELECT \n    time,\n    business_unit,\n    validation_error,    \n    ':' || player_id::text as player_id,\n    ':' || ticket_id::text as ticket_id\nFROM validation\nWHERE validation_error != 'ok'\norder by time desc;\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "Settlement Balance Differences", "type": "table"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 22, "panels": [], "title": "General", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 11}, "id": 34, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-proxy-api\",namespace=\"$namespace\"}[1m])) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-message-router-service\",namespace=\"$namespace\"}[1m])) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_failed_total{job=\"ews-outbound-message-processor-service\",namespace=\"$namespace\"}[1m])) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "C"}], "title": "Errors", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 11}, "id": 36, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-proxy-api\",namespace=\"$namespace\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-message-router-service\",namespace=\"$namespace\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(aspnetcore_healthcheck_status{job=\"ews-outbound-message-processor-service\",namespace=\"$namespace\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "C"}], "title": "Healthchecks", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 11}, "id": 41, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "cache_items_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\", cache=\"DeadLetterKafkaCache\"}", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "title": "Dead Letter Queue (Items Count)", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 43, "panels": [], "title": "Outbound Proxy API", "type": "row"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 44, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 45, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-proxy-api\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 47, "panels": [], "title": "Outbound Message Processor", "type": "row"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "id": 50, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "id": 51, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-message-processor-service\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 37}, "id": 49, "panels": [], "title": "Outbound Message Router", "type": "row"}, {"datasource": null, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 38}, "id": 52, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(execution_duration_seconds_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[1m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Messages Rate", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {}, "thresholdsStyle": {}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 38}, "id": 53, "options": {"alertThreshold": true, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}, "tooltipOptions": {"mode": "single"}}, "pluginVersion": "11.3.0", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "avg(rate(execution_duration_seconds_sum{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[5m])/rate(execution_duration_seconds_count{job=\"ews-outbound-message-router-service\", namespace=\"$namespace\"}[5m])) by (action, controller)", "hide": false, "interval": "", "legendFormat": "{{controller}} {{action}}", "refId": "B"}], "title": "Execution Duration ", "type": "timeseries"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 46}, "id": 40, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 2, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=\"$podname\"}[5m]))", "interval": "", "legendFormat": "cpu usage", "refId": "A"}], "title": "Number of cores used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "id": 24, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_private_memory_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_private_memory_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 26, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_open_handles{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_open_handles", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 32, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_working_set_bytes{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_working_set_bytes", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 30, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_start_time_seconds{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_start_time_seconds", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "id": 28, "options": {"alertThreshold": true}, "pluginVersion": "7.4.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "process_num_threads{instance=\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "process_num_threads", "type": "timeseries"}], "title": "Resources", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 47}, "id": 54, "panels": [], "title": "ALERTS: Seamless Wallet API (SOF-PROD)", "type": "row"}, {"alert": {"alertRuleTags": {}, "conditions": [{"evaluator": {"params": [0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "15m", "now"]}, "reducer": {"params": [], "type": "sum"}, "type": "query"}], "executionErrorState": "alerting", "for": "15m", "frequency": "10m", "handler": 1, "message": "Missing TicketAccepted / TicketRejected Calls alert", "name": "Missing TicketAccepted / TicketRejected Calls alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": true, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL DWH", "description": "", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 48}, "hiddenSeries": false, "id": 55, "interval": null, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"$$hashKey": "object:1928", "aggregation": "Last", "decimals": 2, "displayAliasType": "Warning / Critical", "displayType": "Regular", "displayValueWithAlias": "Never", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n    date_trunc('minute', a.created_date) AS time,\n    a.business_unit,\n    COUNT(1) AS count\nFROM integration.audit_log a\nLEFT JOIN integration.audit_log b\n    ON a.ticket_id = b.ticket_id\n    AND a.id <> b.id\n    AND b.event_type IN ('TicketAcceptedRequestDto', 'TicketRejectedRequestDto')\n    AND b.response_status_code = 200\n    AND (b.response_payload::JSONB)->>'errorCode' = '0'\nWHERE a.event_type = 'TicketReserveAmountRequestDto'\n  AND a.ticket_id != 0\n  AND a.response_status_code = 200\n  AND (a.response_payload::JSONB)->>'errorCode' = '0'\n  AND b.id IS NULL  -- Ensuring missing Ticket Accepted/Rejected\n  AND $__timeFilter(a.created_date) \n  AND a.created_date < NOW() - INTERVAL '20 seconds'\t-- assure not in progress\nGROUP BY time, a.business_unit\nORDER BY time;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "units": "none", "valueHandler": "Number Threshold", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0, "visible": true}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Missing TicketAccepted / TicketRejected Calls", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1689", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1690", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"alertRuleTags": {}, "conditions": [{"evaluator": {"params": [0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "15m", "now"]}, "reducer": {"params": [], "type": "sum"}, "type": "query"}], "executionErrorState": "alerting", "for": "15m", "frequency": "10m", "handler": 1, "message": "Inconsistent Reserved vs Accepted / Rejected Amounts alert", "name": "Inconsistent Reserved vs Accepted / Rejected Amounts alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL DWH", "description": "", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 48}, "hiddenSeries": false, "id": 56, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH req_amount AS (\n    SELECT\n        a.id,\n        a.created_date,\n        a.ticket_id,\n        a.business_unit,\n        a.request_payload->>'TicketAmount' AS request_amount,\n        COALESCE((a.response_payload::JSONB)->>'reservedAmount', \n                a.request_payload->>'TicketAmount') AS reserved_amount,\n        (a.response_payload::JSONB)->>'reserveId' AS reserved_id,\n        b.request_payload->>'ticketAmount' AS ticket_amount,\n        b.request_payload->'bets'->0->>'isFreeBet' AS is_free_bet\n    FROM integration.audit_log a\n    LEFT JOIN integration.audit_log b\n        ON a.ticket_id = b.ticket_id\n        AND a.id != b.id\n        AND b.event_type = 'TicketAcceptedRequestDto'\n        AND b.response_status_code = 200\n    WHERE a.event_type = 'TicketReserveAmountRequestDto'\n        AND a.ticket_id != 0\n        AND a.response_status_code = 200\n        AND (a.response_payload::JSONB)->>'errorCode' = '0'\n        AND b.event_type IS NOT NULL\n        -- AND a.created_date > '2025-02-12 11:00:41.198'\n        AND $__timeFilter(a.created_date)\n),\nticket_amounts AS (\n    SELECT\n        t.id,\n        t.player_id,\n        COALESCE(SUM((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0), 0) AS main_account_stake,\n        COALESCE(SUM((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0), 0) AS bonus_account_amount\n    FROM integration.audit_log al\n    JOIN betting.ticket t ON t.id = al.ticket_id\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\n        AND al.response_status_code = 200\n        AND $__timeFilter(al.created_date)\n    GROUP BY t.id, t.player_id\n),\nticket_partial_cashouts AS (\n    SELECT\n        t.id,\n        t.player_id,\n        COALESCE(SUM((evts->>'usedStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_stake,\n        COALESCE(SUM((evts->>'usedBonusStake')::DECIMAL) FILTER (WHERE evts->>'eventType' = 'PARTIAL_CASH_OUT'), 0) AS used_bonus_stake\n    FROM integration.audit_log al\n    JOIN betting.ticket t ON t.id = al.ticket_id\n    CROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n    LEFT JOIN LATERAL jsonb_array_elements(bts->'events') AS evts ON TRUE\n    WHERE al.event_type = 'TicketAcceptedRequestDto'\n        AND al.response_status_code = 200\n        -- AND al.created_date > '2025-02-12 11:00:41.198'\n        AND $__timeFilter(al.created_date)\n    GROUP BY t.id, t.player_id\n)\nselect\n    date_trunc('minute', ra.created_date) AS time,\n    ra.business_unit,\n    COUNT(*) AS count\nFROM req_amount ra\nJOIN ticket_amounts ta ON ra.ticket_id = ta.id\nJOIN ticket_partial_cashouts tpc ON ra.ticket_id = tpc.id\nWHERE ra.reserved_amount::DECIMAL != COALESCE(ta.main_account_stake, 0) + tpc.used_stake\n    OR (\n        ra.is_free_bet != 'true'\n        AND ta.bonus_account_amount != (ra.request_amount::DECIMAL - ra.reserved_amount::DECIMAL)\n    )\nGROUP BY time, ra.business_unit\nORDER BY time;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0, "visible": true}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Inconsistent Reserved vs Accepted / Rejected Amounts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2911", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2912", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"alertRuleTags": {}, "conditions": [{"evaluator": {"params": [0], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "15m", "now"]}, "reducer": {"params": [], "type": "sum"}, "type": "query"}], "executionErrorState": "alerting", "for": "15m", "frequency": "10m", "handler": 1, "message": "Settlement balance difference detected", "name": "Settlement balance differences alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "PostgreSQL DWH", "description": "", "fieldConfig": {"defaults": {"color": {}, "custom": {}, "thresholds": {"mode": "absolute", "steps": []}, "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 48}, "hiddenSeries": false, "id": 57, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "WITH bets AS (\n\tSELECT\n\t    t.id AS ticket_id,\n\t    t.player_id,\n\t    t.business_unit,\n\t    t.updated_date,\n\t    bts->'id' AS bet_id,\n\t    (bts->'betStatus')::text AS bet_status,\n\t    (bts->'outcomeStatus')::text AS bet_outcome_status,\n\t    COALESCE((bts->'stake'->>'mainMoneyLong')::BIGINT / 10000.0, 0) AS stake_main_acc_amount,\n\t    COALESCE((bts->'stake'->>'bonusMoneyLong')::BIGINT / 10000.0, 0) AS stake_bonus_acc_amount,\n\t    COALESCE((bts->'cashoutStake'->>'valueLong')::BIGINT / 10000.0, 0) AS stake_cashed_out,\n\t    COALESCE((bts->'cashoutBonusStake'->>'valueLong')::BIGINT / 10000.0, 0) AS stake_cashed_out_bonnus,\n\t    SUM(COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0)) as sent_win_amount,\n\t\tSUM(COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0)) as sent_refund_amount,\n\t\tSUM(COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0)) as sent_amend_amount,\n\t\tSUM(COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0)) as sent_cashout_amount,\n\t\tSUM(COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0)) as sent_bonus_amount,\n\t\tSUM(COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)) as sent_revert_amount,\n\t\tSUM(\n\t        COALESCE((al.request_payload->>'winAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'refundAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'amendAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'cashoutAmount')::DECIMAL, 0) +\n\t        COALESCE((al.request_payload->>'bonusAmount')::DECIMAL, 0) +\t        \n\t        COALESCE((al.request_payload->>'revertAmount')::DECIMAL, 0)\n\t    ) AS total_sent_amount\n\tFROM betting.ticket t\n\tCROSS JOIN LATERAL jsonb_array_elements(t.bets) AS bts\n\tLEFT JOIN integration.audit_log al \n\t    ON al.ticket_id = t.id\n\t    AND al.response_status_code = 200\n\t    AND al.created_date >= t.created_date \n\t    AND al.event_type IN ('BetWonRequestDto', 'BetLostRequestDto', 'BetRefundedRequestDto', 'BetAmendedRequestDto',\n\t    \t\t\t\t\t\t'BetCashedOutRequestDto', 'BetBonusAwardDto', 'BetBonusRevertDto')\n\t    AND al.request_payload->>'betId' = bts->>'id'\n\tWHERE t.business_unit IN ('WNRS','EVBA','SLPH')\n\t\t  -- AND t.updated_date > '2025-01-22 13:42:14'\n      AND $__timeFilter(t.updated_date)\n\t    AND t.status NOT IN ('REJECTED', 'ACCEPTED')\n\tGROUP BY t.id, t.player_id, t.business_unit, bts\n),\nmetadata AS (\n    SELECT\n        t.id AS ticket_id,\n        smry->'betId' AS bet_id,\n        COALESCE((smry->'totalWinLong')::BIGINT / 10000.0, 0) AS total_win,\n        COALESCE((smry->'winGrossLong')::BIGINT / 10000.0, 0) AS paid_win,\n        COALESCE((smry->'bonusWinLong')::BIGINT / 10000.0, 0) AS bonus_win,\n        COALESCE((SELECT SUM((campaign->>'amount')::DECIMAL) FROM jsonb_array_elements(smry->'campaigns') AS campaign), 0) AS total_paid_campaign_amount\n    FROM betting.ticket t\n    CROSS JOIN LATERAL jsonb_array_elements(t.meta_data->'summaries') AS smry\n    JOIN integration.business_unit_api_config buac \n        ON buac.business_unit = t.business_unit\n    WHERE EXISTS (SELECT 1 FROM bets WHERE bets.ticket_id = t.id)\n),\nvalidation AS (\n    SELECT\n        bets.updated_date AS time,\n        bets.business_unit,\n        bets.player_id::text,\n        bets.ticket_id::text,\n        CASE \n            WHEN bets.bet_status = '\"CASHED_OUT\"' THEN\n                CASE \n                    WHEN bets.total_sent_amount = bets.stake_cashed_out THEN 'ok' \n                    WHEN ABS(bets.total_sent_amount - bets.stake_cashed_out + bets.stake_cashed_out_bonnus) <= 0.1 THEN 'ok' \n                    ELSE 'different cash out amount' \n                END\n            WHEN bets.bet_outcome_status = '\"VOID\"' THEN \n                CASE \n                    WHEN bets.total_sent_amount = bets.stake_main_acc_amount THEN 'ok'\n\t\t        \tELSE 'invalid loss amount'\n                END\n            WHEN bets.bet_outcome_status = '\"CANCELED\"' THEN \n                CASE \n                    WHEN (bets.stake_main_acc_amount + bets.stake_bonus_acc_amount) = bets.sent_amend_amount \n                    \tOR bets.stake_main_acc_amount = bets.total_sent_amount \n                        OR bets.stake_bonus_acc_amount = bets.sent_amend_amount\n                    THEN 'ok' \n                    ELSE 'different amend amount' \n                END\n\t        WHEN bets.bet_outcome_status = '\"LOSS\"' THEN\n\t        \tCASE \n\t\t        \tWHEN bets.total_sent_amount = 0 AND metadata.total_win = 0 THEN 'ok'\n\t\t        \tWHEN bets.total_sent_amount = bets.sent_bonus_amount \n                        AND (bets.total_sent_amount = metadata.bonus_win + metadata.total_paid_campaign_amount) \n                    THEN 'ok'\n\t\t        \tWHEN bets.total_sent_amount = bets.sent_cashout_amount \n                        AND (bets.total_sent_amount = bets.stake_cashed_out) \n                    THEN 'ok'\n\t\t        \tELSE 'invalid loss amount'\n\t\t        END\n            WHEN bets.bet_outcome_status = '\"WON\"' THEN \n                CASE \n\t                WHEN bets.total_sent_amount = metadata.total_win + bets.stake_cashed_out + metadata.total_paid_campaign_amount \n\t                THEN 'ok' \n                    WHEN ABS(bets.total_sent_amount - metadata.total_win) < 0.1\n                           OR (bets.total_sent_amount = metadata.total_win + metadata.bonus_win)\n                           OR (bets.total_sent_amount = 0 AND bets.stake_main_acc_amount = 0)\n                    THEN 'ok' \n                    ELSE 'different win amount'\n                END\n\t\t\tELSE 'not handled'\n        END AS validation_error\n    FROM bets\n    JOIN metadata ON bets.ticket_id = metadata.ticket_id AND bets.bet_id = metadata.bet_id\n)\nSELECT \n    time,\n    business_unit,\n    COUNT(1) AS count\nFROM validation\nWHERE validation_error != 'ok'\nGROUP BY time, business_unit\nORDER BY time DESC;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0, "visible": true}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Settlement balance differences", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2911", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:2912", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "1m", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "ews-outbound-message-router-service", "value": "ews-outbound-message-router-service"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{}, job)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"query": "label_values(process_private_memory_bytes{}, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/ews-outbound.*/", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-sof-prod", "value": "ews-sof-prod"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "***********:80", "value": "***********:80"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-outbound-message-router-service-d4478dbf7-prx5f", "value": "ews-outbound-message-router-service-d4478dbf7-prx5f"}, "datasource": null, "definition": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 2, "includeAll": false, "label": "Pod", "multi": false, "name": "pod", "options": [], "query": {"query": "label_values(process_private_memory_bytes{job=\"$job\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ews-outbound-message-router-service-d4478dbf7-prx5f", "value": "ews-outbound-message-router-service-d4478dbf7-prx5f"}, "datasource": null, "definition": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "Podname", "multi": false, "name": "podname", "options": [], "query": {"query": "label_values(container_cpu_usage_seconds_total{pod=\"$pod\", namespace=\"$namespace\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Outbound Proxy (Integrations)", "uid": "fas973th3sfd12", "version": 6}