{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-34 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-14 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-15 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-20 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-21 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-22 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-23 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-25 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(sum(vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\",vmname!~\"vCLS-.*\"}) by (vmname) ) / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "sum(vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"}) by (esxhostname) / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-26 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-27 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-28 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 39}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-29 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 46}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-30 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 46}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-31 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 53}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-32 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 53}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-33 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 60}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-35 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 60}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-36 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 67}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-37 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 67}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-38 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 74}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-39 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 74}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-40 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 81}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-41 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 81}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-42 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fieldConfig": {"defaults": {"custom": {}, "unit": "GHz"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 88}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 350, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.4.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "vsphere_vm_cpu_usagemhz_average{cpu=\"instance-total\",source=~\".*\",esxhostname=\"*************\",source!~\"vCLS.*\"} / 1000", "interval": "", "legendFormat": "{{vmname}}", "refId": "A"}, {"expr": "vsphere_host_cpu_usagemhz_average{cpu=\"instance-total\",clustername=\"sof1-prod\",esxhostname=\"*************\"} / 1000", "hide": false, "interval": "", "legendFormat": "MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BG-SOF-ESXI-43 - *************", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "GHz", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:56", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "vSphere CPU consumption by VM", "uid": "9E6rv5E7zbyVMcpu", "version": 1}